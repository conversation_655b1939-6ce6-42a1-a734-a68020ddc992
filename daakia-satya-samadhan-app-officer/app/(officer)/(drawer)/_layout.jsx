import { View, Text, Image, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Drawer } from 'expo-router/drawer';
import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { DrawerContentScrollView, DrawerItemList } from '@react-navigation/drawer';
import { useAuth } from '../../../context/auth-context';
import { transformUrl } from '../../../utils/transformUrl';
import { router } from 'expo-router';
import { Colors } from '../../../constants/colors';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

function CustomDrawerContent(props) {
  const { profile, selectedRole, logout } = useAuth();
  
  if (!profile) {
    return (
      <DrawerContentScrollView {...props}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
        <DrawerItemList {...props} />
      </DrawerContentScrollView>
    );
  }

  const { name, emailId, policeProfile, displayUrl } = profile;
  const transformedImageUrl = transformUrl(displayUrl);

  return (
    <View style={styles.drawerContainer}>
      <DrawerContentScrollView
        {...props}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Modern Profile Card */}
        <View style={styles.profileCard}>
          <View style={styles.profileImageContainer}>
            {transformedImageUrl ? (
              <Image source={{ uri: transformedImageUrl }} style={styles.profileImage} />
            ) : (
              <View style={styles.profileImagePlaceholder}>
                <Text style={styles.profileImagePlaceholderText}>{name?.charAt(0) || "U"}</Text>
              </View>
            )}
            <View style={styles.statusIndicator} />
          </View>

          <View style={styles.profileInfo}>
            <Text style={styles.profileName} numberOfLines={1}>{name}</Text>
            <Text style={styles.profileEmail} numberOfLines={1}>{emailId}</Text>

            <View style={styles.badgesContainer}>
              <View style={styles.roleBadge}>
                <Ionicons name="person-circle-outline" size={14} color={Colors.primary} />
                <Text style={styles.roleBadgeText}>{selectedRole}</Text>
              </View>

              {policeProfile && (
                <View style={styles.designationBadge}>
                  <Ionicons name="shield-checkmark-outline" size={14} color="#10B981" />
                  <Text style={styles.designationBadgeText}>{policeProfile.designation}</Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Navigation Section */}
        <View style={styles.navigationSection}>
          <Text style={styles.sectionTitle}>Menu</Text>
          <View style={styles.navigationContainer}>
            <DrawerItemList {...props} />
          </View>
        </View>
      </DrawerContentScrollView>

      {/* Fixed Bottom Section */}
      <View style={styles.bottomSection}>
        <TouchableOpacity style={styles.logoutButton} onPress={logout}>
          <Ionicons name="power" size={18} color="#EF4444" />
          <Text style={styles.logoutText}>Sign Out</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

export default function DrawerLayoutOfficer() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        drawerContent={(props) => <CustomDrawerContent {...props} />}
        screenOptions={{
          headerShown: false,
          drawerActiveBackgroundColor: Colors.primary,
          drawerActiveTintColor: Colors.background,
          drawerInactiveTintColor: Colors.lightText,
          drawerItemStyle: {
            borderRadius: 12,
            paddingLeft: 8,
            marginHorizontal: 8,
            marginVertical: 2,
          },
          drawerLabelStyle: {
            fontSize: 16,
            fontWeight: '600',
            marginLeft: 8,
          },
          drawerStyle: {
            backgroundColor: Colors.background,
            width: Math.min(SCREEN_WIDTH * 0.85, 350),
          },
        }}
      >
       <Drawer.Screen
  name="(tabs)"
  options={{
    drawerIcon: ({ color }) => (
      <Ionicons name="business-outline" size={22} color={color} />
    ),
    title: "Home",
  }}
  listeners={() => ({
    drawerItemPress: (e) => {
      e.preventDefault(); 

      router.replace('(officer)/(drawer)/(tabs)')
    },
  })}
/>
{/* <Drawer.Screen
  name="(tabs)/profile"
  options={{
    drawerIcon: ({ color }) => (
      <Ionicons name="business-outline" size={22} color={color} />
    ),
    title: "Home",
  }}
  listeners={() => ({
    drawerItemPress: (e) => {
      e.preventDefault(); 

      router.replace('(officer)/(drawer)/(tabs)/profile')
    },
  })}
/> */}
      </Drawer>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 80,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '600',
  },

  // Modern Profile Card
  profileCard: {
    backgroundColor: Colors.background,
    marginHorizontal: 16,
    marginTop: 20,
    marginBottom: 24,
    borderRadius: 20,
    padding: 20,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  profileImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 3,
    borderColor: '#E5E7EB',
  },
  profileImagePlaceholder: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#E5E7EB',
  },
  profileImagePlaceholderText: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.primary,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#10B981',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  profileInfo: {
    alignItems: 'center',
  },
  profileName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
    textAlign: 'center',
  },
  profileEmail: {
    fontSize: 13,
    color: '#6B7280',
    marginBottom: 12,
    textAlign: 'center',
  },
  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  roleBadge: {
    backgroundColor: '#EFF6FF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#DBEAFE',
  },
  roleBadgeText: {
    fontSize: 11,
    color: Colors.primary,
    fontWeight: '600',
    marginLeft: 4,
  },
  designationBadge: {
    backgroundColor: '#ECFDF5',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#D1FAE5',
  },
  designationBadgeText: {
    fontSize: 11,
    color: '#10B981',
    fontWeight: '600',
    marginLeft: 4,
  },

  // Navigation Section
  navigationSection: {
    flex: 1,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
    marginBottom: 12,
    marginLeft: 8,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  navigationContainer: {
    paddingVertical: 8,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },

  // Bottom Section
  bottomSection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.background,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FEF2F2',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  logoutText: {
    color: '#EF4444',
    fontWeight: '600',
    fontSize: 15,
    marginLeft: 8,
  },
});